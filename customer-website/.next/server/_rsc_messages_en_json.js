"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_en_json";
exports.ids = ["_rsc_messages_en_json"];
exports.modules = {

/***/ "(rsc)/./messages/en.json":
/*!**************************!*\
  !*** ./messages/en.json ***!
  \**************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"home":{"title":"Delicious food, delivered fast","subtitle":"Discover amazing restaurants near you and get your favorite meals delivered in minutes","searchPlaceholder":"Search for restaurants or cuisines...","addressPlaceholder":"Enter delivery address","restaurantsNearYou":"Restaurants near you"},"header":{"home":"Home","restaurants":"Restaurants","orders":"My Orders","account":"Account","cart":"Cart","signIn":"Sign In","signOut":"Sign Out"},"restaurants":{"title":"Restaurants","searchPlaceholder":"Search restaurants, cuisines, or dishes...","noResults":"No restaurants found matching your criteria","clearFilters":"Clear Filters"},"cart":{"title":"Your Cart","empty":"Your cart is empty","emptySubtitle":"Add some delicious items to get started!","subtotal":"Subtotal","deliveryFee":"Delivery Fee","serviceFee":"Service Fee","tax":"Tax","total":"Total","promoCode":"Promo Code","applyPromo":"Apply","placeOrder":"Place Order","estimatedDelivery":"Estimated delivery"},"orders":{"title":"My Orders","activeOrders":"Active Orders","pastOrders":"Past Orders","noActiveOrders":"No active orders","noPastOrders":"No past orders","orderNumber":"Order #","reorder":"Reorder","viewDetails":"View Details","cancelOrder":"Cancel Order","rateOrder":"Rate Order"},"auth":{"signIn":"Sign In","signUp":"Sign Up","email":"Email","password":"Password","confirmPassword":"Confirm Password","firstName":"First Name","lastName":"Last Name","forgotPassword":"Forgot Password?","dontHaveAccount":"Don\'t have an account?","alreadyHaveAccount":"Already have an account?","signInWithGoogle":"Sign in with Google","signInWithFacebook":"Sign in with Facebook"},"common":{"loading":"Loading...","error":"Error","success":"Success","cancel":"Cancel","save":"Save","edit":"Edit","delete":"Delete","confirm":"Confirm","back":"Back","next":"Next","previous":"Previous","close":"Close","open":"Open","closed":"Closed","available":"Available","unavailable":"Unavailable","addToCart":"Add to Cart","removeFromCart":"Remove from Cart","quantity":"Quantity","price":"Price","rating":"Rating","reviews":"Reviews","deliveryTime":"Delivery Time","distance":"Distance","cuisine":"Cuisine","popular":"Popular","new":"New","recommended":"Recommended"}}');

/***/ })

};
;